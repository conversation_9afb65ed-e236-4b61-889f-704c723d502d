2025-06-26 13:00:18,736 - XF<PERSON><PERSON><PERSON><PERSON><PERSON> - ERROR - Failed to connect to database: connection to server at "localhost" (127.0.0.1), port 5432 failed: FATAL:  password authentication failed for user "postgres"
connection to server at "localhost" (127.0.0.1), port 5432 failed: FATAL:  password authentication failed for user "postgres"

2025-06-26 13:01:09,141 - XF<PERSON><PERSON><PERSON><PERSON>er - INFO - Connected to the database
2025-06-26 13:01:09,141 - XFWBParser - INFO - XFWB Parser initialized with modular architecture
2025-06-26 13:01:09,142 - XFWBParser - INFO - Starting to parse XFWB file: laraval-side/storage/app/private/xml-imports/1750933764_176-06673063.xml
2025-06-26 13:01:09,142 - XFWBParser - INFO - Starting to parse XFWB XML string
2025-06-26 13:01:09,143 - XFWBParser - INFO - XML parsed successfully
2025-06-26 13:01:09,143 - XFWBParser - INFO - Extracting data from XML
2025-06-26 13:01:09,146 - XFWBParser - INFO - Extracted data for AWB: 176-06673063
2025-06-26 13:01:09,146 - XFWBParser - INFO - Validating extracted data
2025-06-26 13:01:09,146 - XFWBParser - INFO - Saving data to database
2025-06-26 13:01:09,152 - XFWBParser - INFO - Comparing AWB 176-06673063: Existing(4p, 74.40kg, partial=False) vs Incoming(4p, 74.4kg)
2025-06-26 13:01:09,153 - XFWBParser - ERROR - Error creating partial for AWB 176-06673063: unsupported operand type(s) for -: 'float' and 'decimal.Decimal'
2025-06-26 13:01:09,154 - XFWBParser - ERROR - Failed to save data to database
2025-06-26 13:01:09,155 - XFWBParser - INFO - Database cursor closed
2025-06-26 13:01:09,157 - XFWBParser - INFO - Database connection closed
2025-06-26 13:01:46,859 - XFWBParser - INFO - Connected to the database
2025-06-26 13:01:46,860 - XFWBParser - INFO - XFWB Parser initialized with modular architecture
2025-06-26 13:01:46,860 - XFWBParser - INFO - Starting to parse XFWB file: laraval-side/storage/app/private/xml-imports/1750933764_176-06673063.xml
2025-06-26 13:01:46,861 - XFWBParser - INFO - Starting to parse XFWB XML string
2025-06-26 13:01:46,861 - XFWBParser - INFO - XML parsed successfully
2025-06-26 13:01:46,861 - XFWBParser - INFO - Extracting data from XML
2025-06-26 13:01:46,864 - XFWBParser - INFO - Extracted data for AWB: 176-06673063
2025-06-26 13:01:46,864 - XFWBParser - INFO - Validating extracted data
2025-06-26 13:01:46,865 - XFWBParser - INFO - Saving data to database
2025-06-26 13:01:46,871 - XFWBParser - INFO - Comparing AWB 176-06673063: Existing(4p, 74.40kg, partial=False) vs Incoming(4p, 74.4kg)
2025-06-26 13:01:46,873 - XFWBParser - INFO - Created partial waybill PWB-f2d3f132-0e4d-4599-9421-765085345368 for originally received portion of AWB 176-06673063
2025-06-26 13:01:46,874 - XFWBParser - WARNING - AWB 176-06673063 totals increased from (4p, 74.40kg) to (4p, 74.4kg) - partial created
2025-06-26 13:01:46,881 - XFWBParser - INFO - Created new shipper: DUMMY CONSIGNOR (ID: 53)
2025-06-26 13:01:46,882 - XFWBParser - INFO - Created new consignee: DUMMY CONSIGNEE (ID: 1)
2025-06-26 13:01:46,883 - XFWBParser - INFO - Updated AWB 8 with party information: {'shipper_code': 53, 'consignee_code': 1}
2025-06-26 13:01:46,884 - XFWBParser - INFO - Successfully saved XFWB data for AWB 176-06673063
2025-06-26 13:01:46,884 - XFWBParser - INFO - Successfully processed XFWB for AWB 176-06673063
2025-06-26 13:01:46,885 - XFWBParser - INFO - Database cursor closed
2025-06-26 13:01:46,885 - XFWBParser - INFO - Database connection closed
2025-06-26 13:02:07,809 - XFWBParser - INFO - Connected to the database
2025-06-26 13:02:07,810 - XFWBParser - INFO - XFWB Parser initialized with modular architecture
2025-06-26 13:02:07,810 - XFWBParser - INFO - Starting to parse XFWB file: laraval-side/storage/app/private/xml-imports/1750933734_167-05551560.xml
2025-06-26 13:02:07,810 - XFWBParser - INFO - Starting to parse XFWB XML string
2025-06-26 13:02:07,811 - XFWBParser - INFO - XML parsed successfully
2025-06-26 13:02:07,811 - XFWBParser - INFO - Extracting data from XML
2025-06-26 13:02:07,813 - XFWBParser - INFO - Extracted data for AWB: 167-05551560
2025-06-26 13:02:07,814 - XFWBParser - INFO - Validating extracted data
2025-06-26 13:02:07,814 - XFWBParser - INFO - Saving data to database
2025-06-26 13:02:07,820 - XFWBParser - INFO - Comparing AWB 167-05551560: Existing(3p, 71.70kg, partial=False) vs Incoming(3p, 71.7kg)
2025-06-26 13:02:07,822 - XFWBParser - INFO - Created partial waybill PWB-15db0070-588f-4cbb-be9e-4fbdbb5eec42 for originally received portion of AWB 167-05551560
2025-06-26 13:02:07,823 - XFWBParser - WARNING - AWB 167-05551560 totals increased from (3p, 71.70kg) to (3p, 71.7kg) - partial created
2025-06-26 13:02:07,825 - XFWBParser - INFO - Updated AWB 5 with party information: {'shipper_code': 53, 'consignee_code': 1}
2025-06-26 13:02:07,827 - XFWBParser - INFO - Saved special handling code ELM with ID 1
2025-06-26 13:02:07,829 - XFWBParser - INFO - Successfully saved XFWB data for AWB 167-05551560
2025-06-26 13:02:07,829 - XFWBParser - INFO - Successfully processed XFWB for AWB 167-05551560
2025-06-26 13:02:07,831 - XFWBParser - INFO - Database cursor closed
2025-06-26 13:02:07,831 - XFWBParser - INFO - Database connection closed
2025-06-26 13:10:01,694 - XFWBParser - INFO - Connected to the database
2025-06-26 13:10:01,694 - XFWBParser - INFO - XFWB Parser initialized with modular architecture
2025-06-26 13:10:01,695 - XFWBParser - INFO - Starting to parse XFWB file: laraval-side/storage/app/private/xml-imports/1750933764_176-06673063.xml
2025-06-26 13:10:01,695 - XFWBParser - INFO - Starting to parse XFWB XML string
2025-06-26 13:10:01,696 - XFWBParser - INFO - XML parsed successfully
2025-06-26 13:10:01,696 - XFWBParser - INFO - Extracting data from XML
2025-06-26 13:10:01,698 - XFWBParser - INFO - Extracted data for AWB: 176-06673063
2025-06-26 13:10:01,698 - XFWBParser - INFO - Validating extracted data
2025-06-26 13:10:01,699 - XFWBParser - INFO - Saving data to database
2025-06-26 13:10:01,706 - XFWBParser - INFO - Comparing AWB 176-06673063: Existing(4p, 74.40kg, partial=True) vs Incoming(4p, 74.4kg)
2025-06-26 13:10:01,707 - XFWBParser - INFO - Updating existing AWB 176-06673063 with XFWB data
2025-06-26 13:10:01,708 - XFWBParser - INFO - Updating XML content (8347 characters) for AWB 176-06673063
2025-06-26 13:10:01,709 - XFWBParser - INFO - Updated master waybill with ID 8
2025-06-26 13:10:01,712 - XFWBParser - INFO - Updated AWB 8 with party information: {'shipper_code': 53, 'consignee_code': 1}
2025-06-26 13:10:01,713 - XFWBParser - INFO - Successfully saved XFWB data for AWB 176-06673063
2025-06-26 13:10:01,713 - XFWBParser - INFO - Successfully processed XFWB for AWB 176-06673063
2025-06-26 13:10:01,714 - XFWBParser - INFO - Database cursor closed
2025-06-26 13:10:01,714 - XFWBParser - INFO - Database connection closed
2025-06-26 13:10:13,007 - XFWBParser - INFO - Connected to the database
2025-06-26 13:10:13,008 - XFWBParser - INFO - XFWB Parser initialized with modular architecture
2025-06-26 13:10:13,009 - XFWBParser - INFO - Starting to parse XFWB file: laraval-side/storage/app/private/xml-imports/1750933734_167-05551560.xml
2025-06-26 13:10:13,009 - XFWBParser - INFO - Starting to parse XFWB XML string
2025-06-26 13:10:13,009 - XFWBParser - INFO - XML parsed successfully
2025-06-26 13:10:13,010 - XFWBParser - INFO - Extracting data from XML
2025-06-26 13:10:13,014 - XFWBParser - INFO - Extracted data for AWB: 167-05551560
2025-06-26 13:10:13,014 - XFWBParser - INFO - Validating extracted data
2025-06-26 13:10:13,015 - XFWBParser - INFO - Saving data to database
2025-06-26 13:10:13,020 - XFWBParser - INFO - Comparing AWB 167-05551560: Existing(3p, 71.70kg, partial=True) vs Incoming(3p, 71.7kg)
2025-06-26 13:10:13,021 - XFWBParser - INFO - Updating existing AWB 167-05551560 with XFWB data
2025-06-26 13:10:13,022 - XFWBParser - INFO - Updating XML content (8471 characters) for AWB 167-05551560
2025-06-26 13:10:13,023 - XFWBParser - INFO - Updated master waybill with ID 5
2025-06-26 13:10:13,026 - XFWBParser - INFO - Updated AWB 5 with party information: {'shipper_code': 53, 'consignee_code': 1}
2025-06-26 13:10:13,028 - XFWBParser - INFO - Special handling code ELM already exists for AWB 5
2025-06-26 13:10:13,029 - XFWBParser - INFO - Successfully saved XFWB data for AWB 167-05551560
2025-06-26 13:10:13,029 - XFWBParser - INFO - Successfully processed XFWB for AWB 167-05551560
2025-06-26 13:10:13,031 - XFWBParser - INFO - Database cursor closed
2025-06-26 13:10:13,031 - XFWBParser - INFO - Database connection closed
