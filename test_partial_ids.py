#!/usr/bin/env python3
"""
Test script to demonstrate the new user-friendly partial ID format.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.base_operations import DatabaseOperations
from services.partial_detection_service import PartialDetectionService
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_partial_id_generation():
    """Test the new sequential partial ID generation."""
    
    # Initialize database operations
    db_ops = DatabaseOperations()
    
    # Initialize partial detection service
    partial_service = PartialDetectionService(db_ops, logger)
    
    # Test AWB number
    test_awb = "999-12345678"
    
    print(f"\n=== Testing Partial ID Generation for AWB: {test_awb} ===")
    
    # Simulate scenario where incoming XFWB has more pieces/weight than existing
    # This should trigger partial creation
    result = partial_service.detect_partial_from_totals_comparison(
        awb_number=test_awb,
        incoming_pieces=10,  # More than what would be in database
        incoming_weight=150.0,  # More than what would be in database  
        source="XFWB",
        is_house=False
    )
    
    print(f"Result: {result}")
    
    if result.get("partial_created"):
        print(f"✅ Partial created with ID: {result['partial_id']}")
        print(f"✅ New format: {test_awb}-X (where X is sequence number)")
    else:
        print("ℹ️  No partial created (AWB may not exist or totals are equal)")
    
    # Close database connection
    db_ops.close()

if __name__ == "__main__":
    test_partial_id_generation()
