2025-06-26 13:00:18,714 - XFWBProcessor - INFO - Starting to process XFWB file: laraval-side/storage/app/private/xml-imports/1750933764_176-06673063.xml
2025-06-26 13:00:18,736 - XF<PERSON><PERSON><PERSON>arser - ERROR - Failed to connect to database: connection to server at "localhost" (127.0.0.1), port 5432 failed: FATAL:  password authentication failed for user "postgres"
connection to server at "localhost" (127.0.0.1), port 5432 failed: FATAL:  password authentication failed for user "postgres"

2025-06-26 13:00:18,737 - XFWBProcessor - ERROR - Error processing XFWB file: connection to server at "localhost" (127.0.0.1), port 5432 failed: FATAL:  password authentication failed for user "postgres"
connection to server at "localhost" (127.0.0.1), port 5432 failed: FATAL:  password authentication failed for user "postgres"

2025-06-26 13:00:18,739 - XFWBProcessor - ERROR - Traceback (most recent call last):
  File "/var/www/aircargomis/python/xml-parsers/process_xfwb.py", line 63, in process_xfwb
    parser = XFWBParser(user_id=user_id, branch_id=branch_id)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/var/www/aircargomis/python/xml-parsers/parsers/xfwb_parser.py", line 43, in __init__
    super().__init__(user_id, branch_id, enable_profiling)
  File "/var/www/aircargomis/python/xml-parsers/parsers/base_xml_parser.py", line 57, in __init__
    self._connect_to_db()
  File "/var/www/aircargomis/python/xml-parsers/parsers/base_xml_parser.py", line 100, in _connect_to_db
    self.db_connection = psycopg2.connect(**DATABASE_CONFIG)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/var/www/aircargomis/python/xml-parsers/venv/lib/python3.12/site-packages/psycopg2/__init__.py", line 122, in connect
    conn = _connect(dsn, connection_factory=connection_factory, **kwasync)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
psycopg2.OperationalError: connection to server at "localhost" (127.0.0.1), port 5432 failed: FATAL:  password authentication failed for user "postgres"
connection to server at "localhost" (127.0.0.1), port 5432 failed: FATAL:  password authentication failed for user "postgres"


2025-06-26 13:00:18,741 - XFWBProcessor - INFO - Profiling results:
         1234 function calls (1226 primitive calls) in 0.025 seconds

   Ordered by: cumulative time
   List reduced from 189 to 30 due to restriction <30>

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
        1    0.000    0.000    0.023    0.023 /var/www/aircargomis/python/xml-parsers/parsers/xfwb_parser.py:34(__init__)
        1    0.000    0.000    0.023    0.023 /var/www/aircargomis/python/xml-parsers/parsers/base_xml_parser.py:38(__init__)
        1    0.000    0.000    0.022    0.022 /var/www/aircargomis/python/xml-parsers/parsers/base_xml_parser.py:95(_connect_to_db)
        1    0.000    0.000    0.022    0.022 /var/www/aircargomis/python/xml-parsers/venv/lib/python3.12/site-packages/psycopg2/__init__.py:80(connect)
        1    0.022    0.022    0.022    0.022 {built-in method psycopg2._psycopg._connect}
        1    0.000    0.000    0.002    0.002 /usr/lib/python3.12/traceback.py:182(format_exc)
        1    0.000    0.000    0.002    0.002 /usr/lib/python3.12/traceback.py:128(format_exception)
        1    0.000    0.000    0.002    0.002 /usr/lib/python3.12/traceback.py:718(__init__)
        1    0.000    0.000    0.002    0.002 /usr/lib/python3.12/traceback.py:399(_extract_from_extended_frame_gen)
       30    0.000    0.000    0.002    0.000 /usr/lib/python3.12/traceback.py:318(line)
        5    0.000    0.000    0.002    0.000 /usr/lib/python3.12/linecache.py:26(getline)
        5    0.000    0.000    0.002    0.000 /usr/lib/python3.12/linecache.py:36(getlines)
        4    0.000    0.000    0.002    0.000 /usr/lib/python3.12/linecache.py:80(updatecache)
        4    0.000    0.000    0.001    0.000 /usr/lib/python3.12/tokenize.py:445(open)
        4    0.000    0.000    0.001    0.000 /usr/lib/python3.12/logging/__init__.py:1660(_log)
        4    0.000    0.000    0.001    0.000 /usr/lib/python3.12/tokenize.py:352(detect_encoding)
        7    0.000    0.000    0.001    0.000 /usr/lib/python3.12/tokenize.py:376(read_or_stop)
        7    0.001    0.000    0.001    0.000 {method 'readline' of '_io.BufferedReader' objects}
        3    0.000    0.000    0.001    0.000 /usr/lib/python3.12/logging/__init__.py:1558(error)
        4    0.000    0.000    0.001    0.000 /usr/lib/python3.12/logging/__init__.py:1686(handle)
        4    0.000    0.000    0.001    0.000 /usr/lib/python3.12/logging/__init__.py:1746(callHandlers)
       10    0.000    0.000    0.001    0.000 /usr/lib/python3.12/logging/__init__.py:1011(handle)
       10    0.000    0.000    0.001    0.000 /usr/lib/python3.12/logging/__init__.py:1148(emit)
        5    0.000    0.000    0.001    0.000 /usr/lib/python3.12/logging/__init__.py:1266(emit)
       10    0.000    0.000    0.001    0.000 /usr/lib/python3.12/logging/__init__.py:1137(flush)
       10    0.001    0.000    0.001    0.000 {method 'flush' of '_io.TextIOWrapper' objects}
        8    0.000    0.000    0.000    0.000 /usr/lib/python3.12/traceback.py:944(format)
        1    0.000    0.000    0.000    0.000 /usr/lib/python3.12/logging/__init__.py:1529(info)
        1    0.000    0.000    0.000    0.000 /usr/lib/python3.12/traceback.py:525(format)
        5    0.000    0.000    0.000    0.000 /usr/lib/python3.12/traceback.py:460(format_frame_summary)



2025-06-26 13:01:09,135 - XFWBProcessor - INFO - Starting to process XFWB file: laraval-side/storage/app/private/xml-imports/1750933764_176-06673063.xml
2025-06-26 13:01:09,141 - XFWBParser - INFO - Connected to the database
2025-06-26 13:01:09,141 - XFWBParser - INFO - XFWB Parser initialized with modular architecture
2025-06-26 13:01:09,142 - XFWBProcessor - INFO - Parsing XFWB file: laraval-side/storage/app/private/xml-imports/1750933764_176-06673063.xml
2025-06-26 13:01:09,142 - XFWBParser - INFO - Starting to parse XFWB file: laraval-side/storage/app/private/xml-imports/1750933764_176-06673063.xml
2025-06-26 13:01:09,142 - XFWBParser - INFO - Starting to parse XFWB XML string
2025-06-26 13:01:09,143 - XFWBParser - INFO - XML parsed successfully
2025-06-26 13:01:09,143 - XFWBParser - INFO - Extracting data from XML
2025-06-26 13:01:09,146 - XFWBParser - INFO - Extracted data for AWB: 176-06673063
2025-06-26 13:01:09,146 - XFWBParser - INFO - Validating extracted data
2025-06-26 13:01:09,146 - XFWBParser - INFO - Saving data to database
2025-06-26 13:01:09,152 - XFWBParser - INFO - Comparing AWB 176-06673063: Existing(4p, 74.40kg, partial=False) vs Incoming(4p, 74.4kg)
2025-06-26 13:01:09,153 - XFWBParser - ERROR - Error creating partial for AWB 176-06673063: unsupported operand type(s) for -: 'float' and 'decimal.Decimal'
2025-06-26 13:01:09,154 - XFWBParser - ERROR - Failed to save data to database
2025-06-26 13:01:09,154 - XFWBProcessor - INFO - Parsing completed in 0.01 seconds
2025-06-26 13:01:09,154 - XFWBProcessor - ERROR - Error processing XFWB file: Failed to parse XFWB file: Failed to update AWB totals during partial processing
2025-06-26 13:01:09,155 - XFWBProcessor - ERROR - Traceback (most recent call last):
  File "/var/www/aircargomis/python/xml-parsers/process_xfwb.py", line 76, in process_xfwb
    raise Exception(error_msg)
Exception: Failed to parse XFWB file: Failed to update AWB totals during partial processing

2025-06-26 13:01:09,155 - XFWBParser - INFO - Database cursor closed
2025-06-26 13:01:09,157 - XFWBParser - INFO - Database connection closed
2025-06-26 13:01:09,162 - XFWBProcessor - INFO - Profiling results:
         6483 function calls (6426 primitive calls) in 0.023 seconds

   Ordered by: cumulative time
   List reduced from 471 to 30 due to restriction <30>

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
        1    0.000    0.000    0.012    0.012 /var/www/aircargomis/python/xml-parsers/parsers/xfwb_parser.py:54(parse_file)
        1    0.000    0.000    0.012    0.012 /var/www/aircargomis/python/xml-parsers/parsers/xfwb_parser.py:98(parse_string)
        1    0.000    0.000    0.007    0.007 /var/www/aircargomis/python/xml-parsers/database/xfwb_operations.py:41(save_data)
        1    0.000    0.000    0.006    0.006 /var/www/aircargomis/python/xml-parsers/parsers/xfwb_parser.py:34(__init__)
        1    0.000    0.000    0.006    0.006 /var/www/aircargomis/python/xml-parsers/parsers/base_xml_parser.py:38(__init__)
        1    0.000    0.000    0.006    0.006 /var/www/aircargomis/python/xml-parsers/parsers/base_xml_parser.py:95(_connect_to_db)
        1    0.000    0.000    0.006    0.006 /var/www/aircargomis/python/xml-parsers/venv/lib/python3.12/site-packages/psycopg2/__init__.py:80(connect)
        1    0.006    0.006    0.006    0.006 {built-in method psycopg2._psycopg._connect}
       19    0.000    0.000    0.005    0.000 /usr/lib/python3.12/logging/__init__.py:1660(_log)
       19    0.000    0.000    0.005    0.000 /usr/lib/python3.12/logging/__init__.py:1686(handle)
       19    0.000    0.000    0.004    0.000 /usr/lib/python3.12/logging/__init__.py:1746(callHandlers)
      6/1    0.000    0.000    0.004    0.004 <frozen importlib._bootstrap>:1349(_find_and_load)
       66    0.000    0.000    0.004    0.000 /usr/lib/python3.12/logging/__init__.py:1011(handle)
      6/1    0.000    0.000    0.004    0.004 <frozen importlib._bootstrap>:1304(_find_and_load_unlocked)
       15    0.000    0.000    0.004    0.000 /usr/lib/python3.12/logging/__init__.py:1529(info)
       66    0.000    0.000    0.004    0.000 /usr/lib/python3.12/logging/__init__.py:1148(emit)
      4/2    0.000    0.000    0.004    0.002 <frozen importlib._bootstrap>:911(_load_unlocked)
      4/2    0.000    0.000    0.004    0.002 <frozen importlib._bootstrap_external>:989(exec_module)
     12/4    0.000    0.000    0.004    0.001 <frozen importlib._bootstrap>:480(_call_with_frames_removed)
        1    0.000    0.000    0.003    0.003 /var/www/aircargomis/python/xml-parsers/parsers/base_xml_parser.py:121(close)
        4    0.000    0.000    0.003    0.001 <frozen importlib._bootstrap_external>:1062(get_code)
       33    0.000    0.000    0.003    0.000 /usr/lib/python3.12/logging/__init__.py:1266(emit)
       66    0.000    0.000    0.002    0.000 /usr/lib/python3.12/logging/__init__.py:1137(flush)
        1    0.000    0.000    0.002    0.002 /var/www/aircargomis/python/xml-parsers/services/partial_detection_service.py:36(detect_partial_from_totals_comparison)
       66    0.002    0.000    0.002    0.000 {method 'flush' of '_io.TextIOWrapper' objects}
        1    0.000    0.000    0.002    0.002 <frozen importlib._bootstrap_external>:1054(source_to_code)
        2    0.002    0.001    0.002    0.001 {built-in method builtins.compile}
        1    0.000    0.000    0.002    0.002 /var/www/aircargomis/python/xml-parsers/extractors/xfwb_extractor.py:67(extract)
      4/2    0.000    0.000    0.002    0.001 {built-in method builtins.exec}
        1    0.000    0.000    0.002    0.002 /var/www/aircargomis/python/xml-parsers/services/partial_detection_service.py:1(<module>)



2025-06-26 13:01:46,850 - XFWBProcessor - INFO - Starting to process XFWB file: laraval-side/storage/app/private/xml-imports/1750933764_176-06673063.xml
2025-06-26 13:01:46,859 - XFWBParser - INFO - Connected to the database
2025-06-26 13:01:46,860 - XFWBParser - INFO - XFWB Parser initialized with modular architecture
2025-06-26 13:01:46,860 - XFWBProcessor - INFO - Parsing XFWB file: laraval-side/storage/app/private/xml-imports/1750933764_176-06673063.xml
2025-06-26 13:01:46,860 - XFWBParser - INFO - Starting to parse XFWB file: laraval-side/storage/app/private/xml-imports/1750933764_176-06673063.xml
2025-06-26 13:01:46,861 - XFWBParser - INFO - Starting to parse XFWB XML string
2025-06-26 13:01:46,861 - XFWBParser - INFO - XML parsed successfully
2025-06-26 13:01:46,861 - XFWBParser - INFO - Extracting data from XML
2025-06-26 13:01:46,864 - XFWBParser - INFO - Extracted data for AWB: 176-06673063
2025-06-26 13:01:46,864 - XFWBParser - INFO - Validating extracted data
2025-06-26 13:01:46,865 - XFWBParser - INFO - Saving data to database
2025-06-26 13:01:46,871 - XFWBParser - INFO - Comparing AWB 176-06673063: Existing(4p, 74.40kg, partial=False) vs Incoming(4p, 74.4kg)
2025-06-26 13:01:46,873 - XFWBParser - INFO - Created partial waybill PWB-f2d3f132-0e4d-4599-9421-765085345368 for originally received portion of AWB 176-06673063
2025-06-26 13:01:46,874 - XFWBParser - WARNING - AWB 176-06673063 totals increased from (4p, 74.40kg) to (4p, 74.4kg) - partial created
2025-06-26 13:01:46,881 - XFWBParser - INFO - Created new shipper: DUMMY CONSIGNOR (ID: 53)
2025-06-26 13:01:46,882 - XFWBParser - INFO - Created new consignee: DUMMY CONSIGNEE (ID: 1)
2025-06-26 13:01:46,883 - XFWBParser - INFO - Updated AWB 8 with party information: {'shipper_code': 53, 'consignee_code': 1}
2025-06-26 13:01:46,884 - XFWBParser - INFO - Successfully saved XFWB data for AWB 176-06673063
2025-06-26 13:01:46,884 - XFWBParser - INFO - Successfully processed XFWB for AWB 176-06673063
2025-06-26 13:01:46,884 - XFWBProcessor - INFO - Parsing completed in 0.02 seconds
2025-06-26 13:01:46,884 - XFWBProcessor - INFO - Successfully processed XFWB file: laraval-side/storage/app/private/xml-imports/1750933764_176-06673063.xml
2025-06-26 13:01:46,885 - XFWBProcessor - INFO - Processed waybill with AWB number: 176-06673063
2025-06-26 13:01:46,885 - XFWBProcessor - INFO - AWB ID: 8
2025-06-26 13:01:46,885 - XFWBProcessor - INFO - Total processing time: 0.03 seconds
2025-06-26 13:01:46,885 - XFWBProcessor - WARNING - Processing warnings:
2025-06-26 13:01:46,885 - XFWBProcessor - WARNING -   - Created partial waybill PWB-f2d3f132-0e4d-4599-9421-765085345368 for originally received portion
2025-06-26 13:01:46,885 - XFWBParser - INFO - Database cursor closed
2025-06-26 13:01:46,885 - XFWBParser - INFO - Database connection closed
2025-06-26 13:01:46,890 - XFWBProcessor - INFO - Profiling results:
         7623 function calls (7574 primitive calls) in 0.035 seconds

   Ordered by: cumulative time
   List reduced from 429 to 30 due to restriction <30>

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
        1    0.000    0.000    0.024    0.024 /var/www/aircargomis/python/xml-parsers/parsers/xfwb_parser.py:54(parse_file)
        1    0.000    0.000    0.024    0.024 /var/www/aircargomis/python/xml-parsers/parsers/xfwb_parser.py:98(parse_string)
        1    0.000    0.000    0.019    0.019 /var/www/aircargomis/python/xml-parsers/database/xfwb_operations.py:41(save_data)
        9    0.000    0.000    0.012    0.001 /var/www/aircargomis/python/xml-parsers/database/base_operations.py:36(execute_query)
        9    0.012    0.001    0.012    0.001 {method 'execute' of 'psycopg2.extensions.cursor' objects}
        1    0.000    0.000    0.010    0.010 /var/www/aircargomis/python/xml-parsers/parsers/xfwb_parser.py:34(__init__)
        1    0.000    0.000    0.009    0.009 /var/www/aircargomis/python/xml-parsers/parsers/base_xml_parser.py:38(__init__)
        1    0.000    0.000    0.009    0.009 /var/www/aircargomis/python/xml-parsers/database/xfwb_operations.py:302(save_party_information)
        1    0.000    0.000    0.009    0.009 /var/www/aircargomis/python/xml-parsers/parsers/base_xml_parser.py:95(_connect_to_db)
        1    0.000    0.000    0.008    0.008 /var/www/aircargomis/python/xml-parsers/venv/lib/python3.12/site-packages/psycopg2/__init__.py:80(connect)
        1    0.008    0.008    0.008    0.008 {built-in method psycopg2._psycopg._connect}
        1    0.000    0.000    0.007    0.007 /var/www/aircargomis/python/xml-parsers/database/xfwb_operations.py:336(find_or_create_shipper)
        4    0.000    0.000    0.006    0.001 /var/www/aircargomis/python/xml-parsers/database/base_operations.py:158(find_record)
       28    0.000    0.000    0.005    0.000 /usr/lib/python3.12/logging/__init__.py:1660(_log)
        3    0.000    0.000    0.005    0.002 /var/www/aircargomis/python/xml-parsers/database/base_operations.py:80(insert_record)
       25    0.000    0.000    0.005    0.000 /usr/lib/python3.12/logging/__init__.py:1529(info)
        1    0.000    0.000    0.005    0.005 /var/www/aircargomis/python/xml-parsers/services/partial_detection_service.py:36(detect_partial_from_totals_comparison)
      6/1    0.000    0.000    0.004    0.004 <frozen importlib._bootstrap>:1349(_find_and_load)
      6/1    0.000    0.000    0.004    0.004 <frozen importlib._bootstrap>:1304(_find_and_load_unlocked)
       28    0.000    0.000    0.004    0.000 /usr/lib/python3.12/logging/__init__.py:1686(handle)
       28    0.000    0.000    0.004    0.000 /usr/lib/python3.12/logging/__init__.py:1746(callHandlers)
      4/2    0.000    0.000    0.004    0.002 <frozen importlib._bootstrap>:911(_load_unlocked)
       94    0.000    0.000    0.004    0.000 /usr/lib/python3.12/logging/__init__.py:1011(handle)
      4/2    0.000    0.000    0.004    0.002 <frozen importlib._bootstrap_external>:989(exec_module)
     12/4    0.000    0.000    0.004    0.001 <frozen importlib._bootstrap>:480(_call_with_frames_removed)
       94    0.000    0.000    0.004    0.000 /usr/lib/python3.12/logging/__init__.py:1148(emit)
        4    0.000    0.000    0.003    0.001 <frozen importlib._bootstrap_external>:1062(get_code)
       47    0.000    0.000    0.002    0.000 /usr/lib/python3.12/logging/__init__.py:1266(emit)
        1    0.000    0.000    0.002    0.002 <frozen importlib._bootstrap_external>:1054(source_to_code)
        1    0.000    0.000    0.002    0.002 /var/www/aircargomis/python/xml-parsers/services/partial_detection_service.py:233(_create_partial_for_originally_received)



2025-06-26 13:02:07,803 - XFWBProcessor - INFO - Starting to process XFWB file: laraval-side/storage/app/private/xml-imports/1750933734_167-05551560.xml
2025-06-26 13:02:07,809 - XFWBParser - INFO - Connected to the database
2025-06-26 13:02:07,810 - XFWBParser - INFO - XFWB Parser initialized with modular architecture
2025-06-26 13:02:07,810 - XFWBProcessor - INFO - Parsing XFWB file: laraval-side/storage/app/private/xml-imports/1750933734_167-05551560.xml
2025-06-26 13:02:07,810 - XFWBParser - INFO - Starting to parse XFWB file: laraval-side/storage/app/private/xml-imports/1750933734_167-05551560.xml
2025-06-26 13:02:07,810 - XFWBParser - INFO - Starting to parse XFWB XML string
2025-06-26 13:02:07,811 - XFWBParser - INFO - XML parsed successfully
2025-06-26 13:02:07,811 - XFWBParser - INFO - Extracting data from XML
2025-06-26 13:02:07,813 - XFWBParser - INFO - Extracted data for AWB: 167-05551560
2025-06-26 13:02:07,814 - XFWBParser - INFO - Validating extracted data
2025-06-26 13:02:07,814 - XFWBParser - INFO - Saving data to database
2025-06-26 13:02:07,820 - XFWBParser - INFO - Comparing AWB 167-05551560: Existing(3p, 71.70kg, partial=False) vs Incoming(3p, 71.7kg)
2025-06-26 13:02:07,822 - XFWBParser - INFO - Created partial waybill PWB-15db0070-588f-4cbb-be9e-4fbdbb5eec42 for originally received portion of AWB 167-05551560
2025-06-26 13:02:07,823 - XFWBParser - WARNING - AWB 167-05551560 totals increased from (3p, 71.70kg) to (3p, 71.7kg) - partial created
2025-06-26 13:02:07,825 - XFWBParser - INFO - Updated AWB 5 with party information: {'shipper_code': 53, 'consignee_code': 1}
2025-06-26 13:02:07,827 - XFWBParser - INFO - Saved special handling code ELM with ID 1
2025-06-26 13:02:07,829 - XFWBParser - INFO - Successfully saved XFWB data for AWB 167-05551560
2025-06-26 13:02:07,829 - XFWBParser - INFO - Successfully processed XFWB for AWB 167-05551560
2025-06-26 13:02:07,829 - XFWBProcessor - INFO - Parsing completed in 0.02 seconds
2025-06-26 13:02:07,829 - XFWBProcessor - INFO - Successfully processed XFWB file: laraval-side/storage/app/private/xml-imports/1750933734_167-05551560.xml
2025-06-26 13:02:07,830 - XFWBProcessor - INFO - Processed waybill with AWB number: 167-05551560
2025-06-26 13:02:07,830 - XFWBProcessor - INFO - AWB ID: 5
2025-06-26 13:02:07,830 - XFWBProcessor - INFO - Total processing time: 0.03 seconds
2025-06-26 13:02:07,830 - XFWBProcessor - WARNING - Processing warnings:
2025-06-26 13:02:07,830 - XFWBProcessor - WARNING -   - Created partial waybill PWB-15db0070-588f-4cbb-be9e-4fbdbb5eec42 for originally received portion
2025-06-26 13:02:07,831 - XFWBParser - INFO - Database cursor closed
2025-06-26 13:02:07,831 - XFWBParser - INFO - Database connection closed
2025-06-26 13:02:07,836 - XFWBProcessor - INFO - Profiling results:
         7439 function calls (7390 primitive calls) in 0.030 seconds

   Ordered by: cumulative time
   List reduced from 416 to 30 due to restriction <30>

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
        1    0.000    0.000    0.019    0.019 /var/www/aircargomis/python/xml-parsers/parsers/xfwb_parser.py:54(parse_file)
        1    0.000    0.000    0.019    0.019 /var/www/aircargomis/python/xml-parsers/parsers/xfwb_parser.py:98(parse_string)
        1    0.000    0.000    0.014    0.014 /var/www/aircargomis/python/xml-parsers/database/xfwb_operations.py:41(save_data)
       27    0.000    0.000    0.009    0.000 /usr/lib/python3.12/logging/__init__.py:1660(_log)
       24    0.000    0.000    0.008    0.000 /usr/lib/python3.12/logging/__init__.py:1529(info)
       27    0.000    0.000    0.008    0.000 /usr/lib/python3.12/logging/__init__.py:1686(handle)
       27    0.000    0.000    0.008    0.000 /usr/lib/python3.12/logging/__init__.py:1746(callHandlers)
       10    0.000    0.000    0.008    0.001 /var/www/aircargomis/python/xml-parsers/database/base_operations.py:36(execute_query)
       90    0.000    0.000    0.008    0.000 /usr/lib/python3.12/logging/__init__.py:1011(handle)
       10    0.007    0.001    0.007    0.001 {method 'execute' of 'psycopg2.extensions.cursor' objects}
        1    0.000    0.000    0.007    0.007 /var/www/aircargomis/python/xml-parsers/parsers/xfwb_parser.py:34(__init__)
        1    0.000    0.000    0.007    0.007 /var/www/aircargomis/python/xml-parsers/parsers/base_xml_parser.py:38(__init__)
       90    0.000    0.000    0.007    0.000 /usr/lib/python3.12/logging/__init__.py:1148(emit)
        1    0.000    0.000    0.007    0.007 /var/www/aircargomis/python/xml-parsers/parsers/base_xml_parser.py:95(_connect_to_db)
        1    0.000    0.000    0.006    0.006 /var/www/aircargomis/python/xml-parsers/venv/lib/python3.12/site-packages/psycopg2/__init__.py:80(connect)
        1    0.006    0.006    0.006    0.006 {built-in method psycopg2._psycopg._connect}
        1    0.000    0.000    0.005    0.005 /var/www/aircargomis/python/xml-parsers/services/partial_detection_service.py:36(detect_partial_from_totals_comparison)
       45    0.000    0.000    0.005    0.000 /usr/lib/python3.12/logging/__init__.py:1266(emit)
        6    0.000    0.000    0.004    0.001 /var/www/aircargomis/python/xml-parsers/database/base_operations.py:158(find_record)
       90    0.000    0.000    0.004    0.000 /usr/lib/python3.12/logging/__init__.py:1137(flush)
       90    0.004    0.000    0.004    0.000 {method 'flush' of '_io.TextIOWrapper' objects}
      6/1    0.000    0.000    0.003    0.003 <frozen importlib._bootstrap>:1349(_find_and_load)
      6/1    0.000    0.000    0.003    0.003 <frozen importlib._bootstrap>:1304(_find_and_load_unlocked)
      4/2    0.000    0.000    0.003    0.001 <frozen importlib._bootstrap>:911(_load_unlocked)
     11/3    0.000    0.000    0.003    0.001 <frozen importlib._bootstrap>:480(_call_with_frames_removed)
      4/2    0.000    0.000    0.003    0.001 <frozen importlib._bootstrap_external>:989(exec_module)
        1    0.000    0.000    0.003    0.003 /var/www/aircargomis/python/xml-parsers/parsers/base_xml_parser.py:121(close)
      4/2    0.000    0.000    0.002    0.001 {built-in method builtins.exec}
        1    0.000    0.000    0.002    0.002 /var/www/aircargomis/python/xml-parsers/services/partial_detection_service.py:1(<module>)
        1    0.000    0.000    0.002    0.002 /var/www/aircargomis/python/xml-parsers/extractors/xfwb_extractor.py:67(extract)



