#!/usr/bin/env python3
"""
Partial AWB Detection Service for XFWB/XFZB parsers.

This service implements comparison logic against existing database records
to detect partial shipments and handle totals discrepancies.
"""

import logging
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Tuple


class PartialDetectionService:
    """
    Service for detecting partial AWBs during XFWB/XFZB parsing.

    Handles three scenarios:
    1. Incoming totals > existing totals (create partial for originally received)
    2. Incoming totals < existing totals (flag for manual review)
    3. Incoming totals = existing totals (no action needed)
    """

    def __init__(self, db_operations, logger=None):
        """
        Initialize the partial detection service.

        Args:
            db_operations: Database operations instance with cursor and connection.
            logger: Logger instance for audit trail.
        """
        self.db_operations = db_operations
        self.logger = logger or logging.getLogger(__name__)

    def detect_partial_from_totals_comparison(
        self,
        awb_number: str,
        incoming_pieces: int,
        incoming_weight: float,
        source: str,
        manifest_id: str = None,
        is_house: bool = False,
    ) -> Dict:
        """
        Compare incoming AWB totals with existing database records to detect partials.

        Args:
            awb_number: AWB number to check.
            incoming_pieces: Pieces from incoming XFWB/XFZB.
            incoming_weight: Weight from incoming XFWB/XFZB.
            source: Source of the data ('XFWB' or 'XFZB').
            manifest_id: Manifest ID if available.
            is_house: True if this is a house waybill.

        Returns:
            Dict with detection results and actions to take.
        """
        result = {
            "action": "NONE",  # NONE, CREATE_PARTIAL, FLAG_REVIEW, UPDATE_TOTALS
            "partial_created": False,
            "partial_id": None,
            "existing_record": None,
            "totals_updated": False,
            "review_required": False,
            "errors": [],
            "warnings": [],
        }

        try:
            # Find existing AWB record
            existing_record = self._find_existing_awb(awb_number, is_house)

            if not existing_record:
                # No existing record, this is a new AWB - no partial detection needed
                result["action"] = "NONE"
                self.logger.debug(
                    f"No existing record found for AWB {awb_number} - new AWB"
                )
                return result

            result["existing_record"] = existing_record
            existing_pieces = existing_record.get("total_pieces", 0)
            existing_weight = existing_record.get("total_weight", 0.0)
            existing_is_partial = existing_record.get("is_partial", False)

            self.logger.info(
                f"Comparing AWB {awb_number}: "
                f"Existing({existing_pieces}p, {existing_weight}kg, partial={existing_is_partial}) "
                f"vs Incoming({incoming_pieces}p, {incoming_weight}kg)"
            )

            # Scenario 1: Incoming totals > existing totals AND existing.is_partial = false
            if (
                incoming_pieces > existing_pieces or incoming_weight > existing_weight
            ) and not existing_is_partial:
                result["action"] = "CREATE_PARTIAL"
                partial_id = self._create_partial_for_originally_received(
                    awb_number,
                    existing_record,
                    incoming_pieces,
                    incoming_weight,
                    source,
                    is_house,
                )
                if partial_id:
                    result["partial_created"] = True
                    result["partial_id"] = partial_id
                    result["totals_updated"] = self._update_awb_totals(
                        awb_number, incoming_pieces, incoming_weight, is_house
                    )

                    self.logger.warning(
                        f"AWB {awb_number} totals increased from "
                        f"({existing_pieces}p, {existing_weight}kg) to "
                        f"({incoming_pieces}p, {incoming_weight}kg) - partial created"
                    )
                else:
                    result["errors"].append("Failed to create partial waybill")

            # Scenario 2: Incoming totals < existing totals
            elif incoming_pieces < existing_pieces or incoming_weight < existing_weight:
                # Check if this is an XFWB correction scenario (XFWB has detailed totals vs XFFM aggregated totals)
                if source == "XFWB" and not existing_is_partial:
                    # This is likely an XFWB providing detailed totals after XFFM aggregation
                    # Create partial for the remaining portion and update master with XFWB totals
                    result["action"] = "XFWB_CORRECTION"

                    partial_id = self._create_partial_for_remaining_portion(
                        awb_number,
                        existing_record,
                        incoming_pieces,
                        incoming_weight,
                        source,
                        is_house,
                    )

                    if partial_id:
                        result["partial_created"] = True
                        result["partial_id"] = partial_id
                        result["totals_updated"] = self._update_awb_totals(
                            awb_number, incoming_pieces, incoming_weight, is_house
                        )

                        self.logger.warning(
                            f"AWB {awb_number} XFWB correction: Updated from XFFM aggregated "
                            f"({existing_pieces}p, {existing_weight}kg) to XFWB detailed "
                            f"({incoming_pieces}p, {incoming_weight}kg) - partial created for remaining"
                        )
                    else:
                        # Even if partial creation failed, we should still update the AWB totals
                        # This is a correction scenario, not an error
                        result["totals_updated"] = self._update_awb_totals(
                            awb_number, incoming_pieces, incoming_weight, is_house
                        )
                        result["warnings"].append(
                            "Failed to create partial waybill for remaining portion, but AWB totals were updated"
                        )
                else:
                    # Traditional decrease scenario - flag for review
                    result["action"] = "FLAG_REVIEW"
                    result["review_required"] = True

                    # Create notification/task for manual review
                    self._create_discrepancy_notification(
                        awb_number,
                        existing_record,
                        incoming_pieces,
                        incoming_weight,
                        source,
                        is_house,
                    )

                    # Flag AWB status as requiring review
                    self._flag_awb_for_review(awb_number, is_house)

                    self.logger.error(
                        f"AWB {awb_number} totals decreased from "
                        f"({existing_pieces}p, {existing_weight}kg) to "
                        f"({incoming_pieces}p, {incoming_weight}kg) - requires manual review"
                    )

                    result["errors"].append(
                        f"AWB totals decreased - manual review required"
                    )

            # Scenario 3: Incoming totals = existing totals
            else:
                result["action"] = "NONE"
                self.logger.debug(
                    f"AWB {awb_number} totals unchanged - no action required"
                )

        except Exception as e:
            self.logger.error(f"Error in partial detection for AWB {awb_number}: {e}")
            result["errors"].append(str(e))

        return result

    def _find_existing_awb(self, awb_number: str, is_house: bool) -> Optional[Dict]:
        """Find existing AWB record in database."""
        try:
            if is_house:
                table = "house_waybills"
                awb_field = "hawb_number"
            else:
                table = "master_waybills"
                awb_field = "awb_number"

            existing_record = self.db_operations.find_record(
                table,
                f"{awb_field} = %s",
                (awb_number,),
                f"{awb_field}, total_pieces, total_weight, is_partial, status, manifest_id",
            )

            if existing_record:
                return {
                    "awb_number": existing_record[0],
                    "total_pieces": existing_record[1],
                    "total_weight": existing_record[2],
                    "is_partial": existing_record[3],
                    "status": existing_record[4],
                    "manifest_id": existing_record[5],
                }

            return None

        except Exception as e:
            self.logger.error(f"Error finding existing AWB {awb_number}: {e}")
            return None

    def _create_partial_for_originally_received(
        self,
        awb_number: str,
        existing_record: Dict,
        new_total_pieces: int,
        new_total_weight: float,
        source: str,
        is_house: bool,
    ) -> Optional[str]:
        """Create partial waybill for originally received portion."""
        try:
            partial_id = f"PWB-{uuid.uuid4()}"

            partial_data = {
                "partial_id": partial_id,
                "master_awb_number": awb_number if not is_house else None,
                "house_awb_number": awb_number if is_house else None,
                "manifest_id": existing_record.get("manifest_id"),
                "total_pieces_in_awb": new_total_pieces,
                "total_weight_in_awb": new_total_weight,
                "weight_unit": "KGM",
                "origin_airport": "XXX",  # Will be updated from AWB data
                "destination_airport": "XXX",  # Will be updated from AWB data
                "expected_pieces": existing_record["total_pieces"],
                "expected_weight": existing_record["total_weight"],
                "received_pieces": existing_record[
                    "total_pieces"
                ],  # Originally received
                "received_weight": existing_record[
                    "total_weight"
                ],  # Originally received
                "remaining_pieces": new_total_pieces - int(existing_record["total_pieces"]),
                "remaining_weight": new_total_weight - float(existing_record["total_weight"]),
                "split_code": "P",
                "split_sequence": 1,
                "consolidation_id": None,
                "status": "RECEIVED",  # Originally received portion
                "source": source,
                "reconciled_at": None,
                "reconciliation_notes": f"Created from {source} totals increase detection",
                "reconciled_by": None,
                "piece_tolerance": None,
                "weight_tolerance": None,
                "mra_success": None,  # Will inherit from master
                "special_handling_code": None,
                "storage_location": None,
                "notes": f"Auto-created from {source} when totals increased",
                "uld_id": None,
                "branch_id": self.db_operations.branch_id,
            }

            # Add timestamps
            self.db_operations.add_timestamps(
                partial_data, self.db_operations.user_id, self.db_operations.user_id
            )

            # Insert partial waybill
            partial_db_id = self.db_operations.insert_record(
                "partial_waybills", partial_data, "id"
            )

            self.logger.info(
                f"Created partial waybill {partial_id} for originally received portion of AWB {awb_number}"
            )

            return partial_id

        except Exception as e:
            self.logger.error(f"Error creating partial for AWB {awb_number}: {e}")
            return None

    def _create_partial_for_remaining_portion(
        self,
        awb_number: str,
        existing_record: Dict,
        xfwb_pieces: int,
        xfwb_weight: float,
        source: str,
        is_house: bool,
    ) -> Optional[str]:
        """Create partial waybill for the remaining portion when XFWB has less than XFFM totals."""
        try:
            partial_id = f"PWB-{uuid.uuid4()}"

            # Calculate remaining portion (what was in XFFM but not in XFWB)
            # Convert to float to handle Decimal types from database
            existing_pieces = int(existing_record["total_pieces"])
            existing_weight = float(existing_record["total_weight"])
            remaining_pieces = existing_pieces - xfwb_pieces
            remaining_weight = existing_weight - xfwb_weight

            partial_data = {
                "partial_id": partial_id,
                "master_awb_number": awb_number if not is_house else None,
                "house_awb_number": awb_number if is_house else None,
                "manifest_id": existing_record.get("manifest_id"),
                "total_pieces_in_awb": xfwb_pieces,  # XFWB totals (what's actually detailed)
                "total_weight_in_awb": xfwb_weight,  # XFWB totals (what's actually detailed)
                "weight_unit": "KGM",
                "origin_airport": "XXX",  # Will be updated from AWB data
                "destination_airport": "XXX",  # Will be updated from AWB data
                "expected_pieces": remaining_pieces,  # What's remaining
                "expected_weight": remaining_weight,  # What's remaining
                "received_pieces": 0,  # Not yet received
                "received_weight": 0.0,  # Not yet received
                "remaining_pieces": remaining_pieces,  # Still pending
                "remaining_weight": remaining_weight,  # Still pending
                "split_code": "P",
                "split_sequence": 2,  # This is the second part
                "consolidation_id": None,
                "status": "PENDING",  # Remaining portion is pending
                "source": source,  # Use original source (XFWB) as it's allowed by enum constraint
                "reconciled_at": None,
                "reconciliation_notes": f"Created from {source} correction - remaining portion after detailed waybill",
                "reconciled_by": None,
                "piece_tolerance": None,
                "weight_tolerance": None,
                "mra_success": None,  # Will inherit from master
                "special_handling_code": None,
                "storage_location": None,
                "notes": f"Auto-created remaining portion when {source} provided detailed totals less than XFFM aggregated totals",
                "uld_id": None,
                "branch_id": self.db_operations.branch_id,
            }

            # Add timestamps
            self.db_operations.add_timestamps(
                partial_data, self.db_operations.user_id, self.db_operations.user_id
            )

            # Insert partial waybill
            partial_db_id = self.db_operations.insert_record(
                "partial_waybills", partial_data, "id"
            )

            self.logger.info(
                f"Created partial waybill {partial_id} for remaining portion of AWB {awb_number} "
                f"({remaining_pieces}p, {remaining_weight}kg remaining after XFWB correction)"
            )

            return partial_id

        except Exception as e:
            self.logger.error(
                f"Error creating remaining portion partial for AWB {awb_number}: {e}"
            )
            return None

    def _update_awb_totals(
        self, awb_number: str, new_pieces: int, new_weight: float, is_house: bool
    ) -> bool:
        """Update AWB totals with new higher values."""
        try:
            if is_house:
                table = "house_waybills"
                awb_field = "hawb_number"
            else:
                table = "master_waybills"
                awb_field = "awb_number"

            update_data = {
                "total_pieces": new_pieces,
                "total_weight": new_weight,
                "is_partial": True,  # Mark as partial since we created a partial record
            }

            self.db_operations.add_timestamps(
                update_data, updated_by=self.db_operations.user_id
            )

            rows_affected = self.db_operations.update_record(
                table, update_data, f"{awb_field} = %s", (awb_number,)
            )

            return rows_affected > 0

        except Exception as e:
            self.logger.error(f"Error updating AWB totals for {awb_number}: {e}")
            return False

    def _create_discrepancy_notification(
        self,
        awb_number: str,
        existing_record: Dict,
        incoming_pieces: int,
        incoming_weight: float,
        source: str,
        is_house: bool,
    ):
        """Create notification for manual review of discrepancy."""
        # This would integrate with the operations dashboard
        # For now, we'll log it as an error that can be picked up by monitoring
        self.logger.error(
            f"DISCREPANCY_NOTIFICATION: AWB {awb_number} ({source}) - "
            f"Totals decreased from ({existing_record['total_pieces']}p, {existing_record['total_weight']}kg) "
            f"to ({incoming_pieces}p, {incoming_weight}kg) - MANUAL REVIEW REQUIRED"
        )

    def _flag_awb_for_review(self, awb_number: str, is_house: bool):
        """Flag AWB status as requiring review."""
        try:
            if is_house:
                table = "house_waybills"
                awb_field = "hawb_number"
            else:
                table = "master_waybills"
                awb_field = "awb_number"

            update_data = {
                "status": "REQUIRES_REVIEW",
            }

            self.db_operations.add_timestamps(
                update_data, updated_by=self.db_operations.user_id
            )

            self.db_operations.update_record(
                table, update_data, f"{awb_field} = %s", (awb_number,)
            )

        except Exception as e:
            self.logger.error(f"Error flagging AWB {awb_number} for review: {e}")
